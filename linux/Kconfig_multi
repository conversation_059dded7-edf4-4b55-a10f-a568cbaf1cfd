#
# yaffs file system configurations
#

config YAFFS_FS
	tristate "yaffs2 file system support"
	default n
	depends on MTD_BLOCK
	select YAFFS_YAFFS1
	select YAFFS_YAFFS2
	help
	  yaffs2, or Yet Another Flash File System, is a file system
	  optimised for NAND Flash chips.

	  To compile the yaffs2 file system support as a module, choose M
	  here: the module will be called yaffs2.

	  If unsure, say N.

	  Further information on yaffs2 is available at
	  <http://www.aleph1.co.uk/yaffs/>.

config YAFFS_YAFFS1
	bool "512 byte / page devices"
	depends on YAFFS_FS
	default y
	help
	  Enable yaffs1 support -- yaffs for 512 byte / page devices

	  Not needed for 2K-page devices.

	  If unsure, say Y.

config YAFFS_9BYTE_TAGS
	bool "Use older-style on-NAND data format with pageStatus byte"
	depends on YAFFS_YAFFS1
	default n
	help

	  Older-style on-NAND data format has a "pageStatus" byte to record
	  chunk/page state.  This byte is zero when the page is discarded.
	  Choose this option if you have existing on-NAND data using this
	  format that you need to continue to support.  New data written
	  also uses the older-style format.  Note: Use of this option
	  generally requires that MTD's oob layout be adjusted to use the
	  older-style format.  See notes on tags formats and MTD versions
	  in yaffs_mtdif1.c.

	  If unsure, say N.

config YAFFS_DOES_ECC
	bool "Lets yaffs do its own ECC"
	depends on YAFFS_FS && YAFFS_YAFFS1 && !YAFFS_9BYTE_TAGS
	default n
	help
	  This enables yaffs to use its own ECC functions instead of using
	  the ones from the generic MTD-NAND driver.

	  If unsure, say N.

config YAFFS_ECC_WRONG_ORDER
	bool "Use the same ecc byte order as Steven Hill's nand_ecc.c"
	depends on YAFFS_FS && YAFFS_DOES_ECC && !YAFFS_9BYTE_TAGS
	default n
	help
	  This makes yaffs_ecc.c use the same ecc byte order as Steven
	  Hill's nand_ecc.c. If not set, then you get the same ecc byte
	  order as SmartMedia.

	  If unsure, say N.

config YAFFS_YAFFS2
	bool "2048 byte (or larger) / page devices"
	depends on YAFFS_FS
	default y
	help
	  Enable yaffs2 support -- yaffs for >= 2K bytes per page devices

	  If unsure, say Y.

config YAFFS_AUTO_YAFFS2
	bool "Autoselect yaffs2 format"
	depends on YAFFS_YAFFS2
	default y
	help
	  Without this, you need to explicitely use yaffs2 as the file
	  system type. With this, you can say "yaffs" and yaffs or yaffs2
	  will be used depending on the device page size (yaffs on
	  512-byte page devices, yaffs2 on 2K page devices).

	  If unsure, say Y.

config YAFFS_DISABLE_TAGS_ECC
	bool "Disable yaffs from doing ECC on tags by default"
	depends on YAFFS_FS && YAFFS_YAFFS2
	default n
	help
	  This defaults yaffs to using its own ECC calculations on tags instead of
	  just relying on the MTD.
	  This behavior can also be overridden with tags_ecc_on and
	  tags_ecc_off mount options.

	  If unsure, say N.

config YAFFS_ALWAYS_CHECK_CHUNK_ERASED
	bool "Force chunk erase check"
	depends on YAFFS_FS
	default n
	help
          Normally yaffs only checks chunks before writing until an erased
	  chunk is found. This helps to detect any partially written
	  chunks that might have happened due to power loss.

	  Enabling this forces on the test that chunks are erased in flash
	  before writing to them. This takes more time but is potentially
	  a bit more secure.

	  Suggest setting Y during development and ironing out driver
	  issues etc. Suggest setting to N if you want faster writing.

	  If unsure, say Y.

config YAFFS_EMPTY_LOST_AND_FOUND
	bool "Empty lost and found on boot"
	depends on YAFFS_FS
	default n
	help
	  If this is enabled then the contents of lost and found is
	  automatically dumped at mount.

	  If unsure, say N.

config YAFFS_DISABLE_BLOCK_REFRESHING
	bool "Disable yaffs2 block refreshing"
	depends on YAFFS_FS
	default n
	help
	 If this is set, then block refreshing is disabled.
	 Block refreshing infrequently refreshes the oldest block in
	 a yaffs2 file system. This mechanism helps to refresh flash to
	 mitigate against data loss. This is particularly useful for MLC.

	  If unsure, say N.

config YAFFS_DISABLE_BACKGROUND
	bool "Disable yaffs2 background processing"
	depends on YAFFS_FS
	default n
	help
	 If this is set, then background processing is disabled.
	 Background processing makes many foreground activities faster.

	 If unsure, say N.

config YAFFS_DISABLE_BAD_BLOCK_MARKING
	bool "Disable yaffs2 bad block marking"
	depends on YAFFS_FS
	default n
	help
	 Useful during early flash bring up to prevent problems causing
	 lots of bad block marking.

	 If unsure, say N.

config YAFFS_XATTR
	bool "Enable yaffs2 xattr support"
	depends on YAFFS_FS
	default y
	help
	 If this is set then yaffs2 will provide xattr support.
	 If unsure, say Y.
