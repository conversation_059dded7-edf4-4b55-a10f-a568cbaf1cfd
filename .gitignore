*~
*.o
*o.cmd
*.ko
*.pyc
DEADJOE
*.mod.c
*.order

/Module.symvers
/.tmp_versions

# .swp files which are generated by open vim sessions.
*.swp

#
# cscope files
#
cscope.*

#emfile
emfile-*

# IDE files used by various developer tools
.idea

# here are the files that are copied into the direct folder. They are modified
# when they are copied. We don't want to track them.

direct/yaffs_allocator.c
direct/yaffs_allocator.h
direct/yaffs_attribs.h
direct/yaffs_bitmap.c
direct/yaffs_bitmap.h
direct/yaffs_cache.c
direct/yaffs_cache.h
direct/yaffs_checkptrw.c
direct/yaffs_checkptrw.h
direct/yaffs_ecc.c
direct/yaffs_ecc.h
direct/yaffs_endian.c
direct/yaffs_endian.h
direct/yaffs_getblockinfo.h
direct/yaffs_guts.c
direct/yaffs_guts.h
direct/yaffs_nameval.c
direct/yaffs_nameval.h
direct/yaffs_nand.c
direct/yaffs_nand.h
direct/yaffs_packedtags1.c
direct/yaffs_packedtags1.h
direct/yaffs_packedtags2.c
direct/yaffs_packedtags2.h
direct/yaffs_summary.c
direct/yaffs_summary.h
direct/yaffs_tagscompat.c
direct/yaffs_tagscompat.h
direct/yaffs_tagsmarshall.c
direct/yaffs_tagsmarshall.h
direct/yaffs_trace.h
direct/yaffs_verify.c
direct/yaffs_verify.h
direct/yaffs_yaffs1.c
direct/yaffs_yaffs1.h
direct/yaffs_yaffs2.c
direct/yaffs_yaffs2.h



# here are all the symlinks that yaffs direct uses
# we don't want to track them.
# this list was created using the command run in the root directory.
# $  find * -type l -not -exec grep -q "^{}$" .gitignore \; -print >> .gitignore

direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nanddrv.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_packedtags2.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nandsim.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_cache.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_hweight.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_ramem2k.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_flexible_file_sim.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_cache.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_m18_drv.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nandstore_file.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nand.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_bitmap.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_yaffs2.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_fileem2k.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_checkptrw.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_error.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_flashif.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_getblockinfo.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_list.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_ecc.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffsfs.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nand_drv.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/ydirectenv.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_allocator.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nanddrv.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_verify.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffsfs.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nandsim.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_ramdisk.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nand_chip.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_summary.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nameval.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_summary.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_checkptrw.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nand_store.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_endian.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_m18_drv.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_tagsmarshall.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_yaffs2.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nandsim_file.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_allocator.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yportenv.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_packedtags2.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_osglue.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_flashif2.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_tagsmarshall.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_endian.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nand_drv.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nandsim_file.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_fileem2k.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nor_drv.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_verify.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nandsim_file.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_packedtags1.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nor_drv.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/ynorsim.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nand.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_attribs.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_fileem.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_yaffs1.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_guts.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffscfg2k.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffscfg.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_flexible_file_sim.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_guts.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/ynorsim.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_ramdisk.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_hweight.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_packedtags1.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_trace.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_tagscompat.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_attribs.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_ecc.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_yaffs1.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_bitmap.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nameval.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nandemul2k.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_tagscompat.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_nandsim_file.h
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/yaffs_osglue.c
direct/test-framework/unit_tests/64_and_32_bit_time/64_bit/nandstore_file.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nanddrv.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_packedtags2.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nandsim.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_cache.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_hweight.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_ramem2k.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_flexible_file_sim.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_cache.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_m18_drv.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nandstore_file.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nand.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_bitmap.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_yaffs2.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_fileem2k.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_checkptrw.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_error.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_flashif.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_getblockinfo.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_list.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_ecc.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffsfs.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nand_drv.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/ydirectenv.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_allocator.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nanddrv.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_verify.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffsfs.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nandsim.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_ramdisk.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nand_chip.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_summary.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nameval.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_summary.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_checkptrw.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nand_store.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_endian.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_m18_drv.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_tagsmarshall.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_yaffs2.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nandsim_file.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_allocator.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yportenv.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_packedtags2.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_osglue.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_flashif2.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_tagsmarshall.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_endian.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nand_drv.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nandsim_file.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_fileem2k.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nor_drv.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_verify.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nandsim_file.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_packedtags1.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nor_drv.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/ynorsim.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nand.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_attribs.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_fileem.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_yaffs1.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_guts.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffscfg2k.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffscfg.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_flexible_file_sim.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_guts.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/ynorsim.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_ramdisk.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_hweight.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_packedtags1.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_trace.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_tagscompat.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_attribs.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_ecc.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_yaffs1.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_bitmap.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nameval.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nandemul2k.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_tagscompat.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_nandsim_file.h
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/yaffs_osglue.c
direct/test-framework/unit_tests/64_and_32_bit_time/32_bit/nandstore_file.c
direct/test-framework/unit_tests/is_yaffs_working_tests/nanddrv.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_packedtags2.h
direct/test-framework/unit_tests/is_yaffs_working_tests/nandsim.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_cache.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_hweight.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_ramem2k.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_flexible_file_sim.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_cache.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_m18_drv.c
direct/test-framework/unit_tests/is_yaffs_working_tests/nandstore_file.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nand.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_bitmap.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_yaffs2.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_fileem2k.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_checkptrw.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_error.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_flashif.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_getblockinfo.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_list.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_ecc.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffsfs.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nand_drv.c
direct/test-framework/unit_tests/is_yaffs_working_tests/ydirectenv.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_allocator.h
direct/test-framework/unit_tests/is_yaffs_working_tests/nanddrv.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_verify.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffsfs.c
direct/test-framework/unit_tests/is_yaffs_working_tests/nandsim.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_ramdisk.h
direct/test-framework/unit_tests/is_yaffs_working_tests/nand_chip.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_summary.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nameval.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_summary.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_checkptrw.c
direct/test-framework/unit_tests/is_yaffs_working_tests/nand_store.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_endian.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_m18_drv.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_tagsmarshall.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_yaffs2.h
direct/test-framework/unit_tests/is_yaffs_working_tests/nandsim_file.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_allocator.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yportenv.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_packedtags2.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_osglue.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_flashif2.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_tagsmarshall.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_endian.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nand_drv.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nandsim_file.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_fileem2k.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nor_drv.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_verify.c
direct/test-framework/unit_tests/is_yaffs_working_tests/nandsim_file.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_packedtags1.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nor_drv.c
direct/test-framework/unit_tests/is_yaffs_working_tests/ynorsim.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nand.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_attribs.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_fileem.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_yaffs1.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_guts.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffscfg2k.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffscfg.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_flexible_file_sim.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_guts.c
direct/test-framework/unit_tests/is_yaffs_working_tests/ynorsim.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_ramdisk.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_hweight.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_packedtags1.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_trace.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_tagscompat.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_attribs.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_ecc.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_yaffs1.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_bitmap.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nameval.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nandemul2k.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_tagscompat.c
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_nandsim_file.h
direct/test-framework/unit_tests/is_yaffs_working_tests/yaffs_osglue.c
direct/test-framework/unit_tests/is_yaffs_working_tests/nandstore_file.c
direct/test-framework/unit_tests/quick_tests/nanddrv.c
direct/test-framework/unit_tests/quick_tests/yaffs_packedtags2.h
direct/test-framework/unit_tests/quick_tests/nandsim.h
direct/test-framework/unit_tests/quick_tests/yaffs_cache.h
direct/test-framework/unit_tests/quick_tests/yaffs_hweight.c
direct/test-framework/unit_tests/quick_tests/yaffs_ramem2k.c
direct/test-framework/unit_tests/quick_tests/yaffs_flexible_file_sim.c
direct/test-framework/unit_tests/quick_tests/yaffs_cache.c
direct/test-framework/unit_tests/quick_tests/yaffs_m18_drv.c
direct/test-framework/unit_tests/quick_tests/nandstore_file.h
direct/test-framework/unit_tests/quick_tests/yaffs_nand.c
direct/test-framework/unit_tests/quick_tests/yaffs_bitmap.h
direct/test-framework/unit_tests/quick_tests/yaffs_yaffs2.c
direct/test-framework/unit_tests/quick_tests/yaffs_fileem2k.h
direct/test-framework/unit_tests/quick_tests/yaffs_checkptrw.h
direct/test-framework/unit_tests/quick_tests/yaffs_error.c
direct/test-framework/unit_tests/quick_tests/yaffs_flashif.h
direct/test-framework/unit_tests/quick_tests/yaffs_getblockinfo.h
direct/test-framework/unit_tests/quick_tests/yaffs_list.h
direct/test-framework/unit_tests/quick_tests/yaffs_ecc.c
direct/test-framework/unit_tests/quick_tests/yaffsfs.h
direct/test-framework/unit_tests/quick_tests/yaffs_nand_drv.c
direct/test-framework/unit_tests/quick_tests/ydirectenv.h
direct/test-framework/unit_tests/quick_tests/yaffs_allocator.h
direct/test-framework/unit_tests/quick_tests/nanddrv.h
direct/test-framework/unit_tests/quick_tests/yaffs_verify.h
direct/test-framework/unit_tests/quick_tests/yaffsfs.c
direct/test-framework/unit_tests/quick_tests/nandsim.c
direct/test-framework/unit_tests/quick_tests/yaffs_ramdisk.h
direct/test-framework/unit_tests/quick_tests/nand_chip.h
direct/test-framework/unit_tests/quick_tests/yaffs_summary.h
direct/test-framework/unit_tests/quick_tests/yaffs_nameval.c
direct/test-framework/unit_tests/quick_tests/yaffs_summary.c
direct/test-framework/unit_tests/quick_tests/yaffs_checkptrw.c
direct/test-framework/unit_tests/quick_tests/nand_store.h
direct/test-framework/unit_tests/quick_tests/yaffs_endian.c
direct/test-framework/unit_tests/quick_tests/yaffs_m18_drv.h
direct/test-framework/unit_tests/quick_tests/yaffs_tagsmarshall.c
direct/test-framework/unit_tests/quick_tests/yaffs_yaffs2.h
direct/test-framework/unit_tests/quick_tests/nandsim_file.c
direct/test-framework/unit_tests/quick_tests/yaffs_allocator.c
direct/test-framework/unit_tests/quick_tests/yportenv.h
direct/test-framework/unit_tests/quick_tests/yaffs_packedtags2.c
direct/test-framework/unit_tests/quick_tests/yaffs_osglue.h
direct/test-framework/unit_tests/quick_tests/yaffs_flashif2.h
direct/test-framework/unit_tests/quick_tests/yaffs_tagsmarshall.h
direct/test-framework/unit_tests/quick_tests/yaffs_endian.h
direct/test-framework/unit_tests/quick_tests/yaffs_nand_drv.h
direct/test-framework/unit_tests/quick_tests/yaffs_nandsim_file.c
direct/test-framework/unit_tests/quick_tests/yaffs_fileem2k.c
direct/test-framework/unit_tests/quick_tests/yaffs_nor_drv.h
direct/test-framework/unit_tests/quick_tests/yaffs_verify.c
direct/test-framework/unit_tests/quick_tests/nandsim_file.h
direct/test-framework/unit_tests/quick_tests/yaffs_packedtags1.c
direct/test-framework/unit_tests/quick_tests/yaffs_nor_drv.c
direct/test-framework/unit_tests/quick_tests/ynorsim.c
direct/test-framework/unit_tests/quick_tests/yaffs_nand.h
direct/test-framework/unit_tests/quick_tests/yaffs_attribs.h
direct/test-framework/unit_tests/quick_tests/yaffs_fileem.c
direct/test-framework/unit_tests/quick_tests/yaffs_yaffs1.c
direct/test-framework/unit_tests/quick_tests/yaffs_guts.h
direct/test-framework/unit_tests/quick_tests/yaffscfg2k.c
direct/test-framework/unit_tests/quick_tests/yaffscfg.h
direct/test-framework/unit_tests/quick_tests/yaffs_flexible_file_sim.h
direct/test-framework/unit_tests/quick_tests/yaffs_guts.c
direct/test-framework/unit_tests/quick_tests/ynorsim.h
direct/test-framework/unit_tests/quick_tests/yaffs_ramdisk.c
direct/test-framework/unit_tests/quick_tests/yaffs_hweight.h
direct/test-framework/unit_tests/quick_tests/yaffs_packedtags1.h
direct/test-framework/unit_tests/quick_tests/yaffs_trace.h
direct/test-framework/unit_tests/quick_tests/yaffs_tagscompat.h
direct/test-framework/unit_tests/quick_tests/yaffs_attribs.c
direct/test-framework/unit_tests/quick_tests/yaffs_ecc.h
direct/test-framework/unit_tests/quick_tests/yaffs_yaffs1.h
direct/test-framework/unit_tests/quick_tests/yaffs_bitmap.c
direct/test-framework/unit_tests/quick_tests/yaffs_nameval.h
direct/test-framework/unit_tests/quick_tests/yaffs_nandemul2k.h
direct/test-framework/unit_tests/quick_tests/yaffs_tagscompat.c
direct/test-framework/unit_tests/quick_tests/yaffs_nandsim_file.h
direct/test-framework/unit_tests/quick_tests/yaffs_osglue.c
direct/test-framework/unit_tests/quick_tests/nandstore_file.c
direct/test-framework/stress_tests/stress_tester/nanddrv.c
direct/test-framework/stress_tests/stress_tester/yaffs_packedtags2.h
direct/test-framework/stress_tests/stress_tester/nandsim.h
direct/test-framework/stress_tests/stress_tester/yaffs_cache.h
direct/test-framework/stress_tests/stress_tester/yaffs_hweight.c
direct/test-framework/stress_tests/stress_tester/yaffs_ramem2k.c
direct/test-framework/stress_tests/stress_tester/yaffs_flexible_file_sim.c
direct/test-framework/stress_tests/stress_tester/yaffs_cache.c
direct/test-framework/stress_tests/stress_tester/yaffs_m18_drv.c
direct/test-framework/stress_tests/stress_tester/nandstore_file.h
direct/test-framework/stress_tests/stress_tester/yaffs_nand.c
direct/test-framework/stress_tests/stress_tester/yaffs_bitmap.h
direct/test-framework/stress_tests/stress_tester/yaffs_yaffs2.c
direct/test-framework/stress_tests/stress_tester/yaffs_fileem2k.h
direct/test-framework/stress_tests/stress_tester/yaffs_checkptrw.h
direct/test-framework/stress_tests/stress_tester/yaffs_error.c
direct/test-framework/stress_tests/stress_tester/yaffs_flashif.h
direct/test-framework/stress_tests/stress_tester/yaffs_getblockinfo.h
direct/test-framework/stress_tests/stress_tester/yaffs_list.h
direct/test-framework/stress_tests/stress_tester/yaffs_ecc.c
direct/test-framework/stress_tests/stress_tester/yaffsfs.h
direct/test-framework/stress_tests/stress_tester/yaffs_nand_drv.c
direct/test-framework/stress_tests/stress_tester/ydirectenv.h
direct/test-framework/stress_tests/stress_tester/yaffs_allocator.h
direct/test-framework/stress_tests/stress_tester/nanddrv.h
direct/test-framework/stress_tests/stress_tester/yaffs_verify.h
direct/test-framework/stress_tests/stress_tester/yaffsfs.c
direct/test-framework/stress_tests/stress_tester/nandsim.c
direct/test-framework/stress_tests/stress_tester/yaffs_ramdisk.h
direct/test-framework/stress_tests/stress_tester/nand_chip.h
direct/test-framework/stress_tests/stress_tester/yaffs_summary.h
direct/test-framework/stress_tests/stress_tester/yaffs_nameval.c
direct/test-framework/stress_tests/stress_tester/yaffs_summary.c
direct/test-framework/stress_tests/stress_tester/yaffs_checkptrw.c
direct/test-framework/stress_tests/stress_tester/nand_store.h
direct/test-framework/stress_tests/stress_tester/yaffs_endian.c
direct/test-framework/stress_tests/stress_tester/yaffs_m18_drv.h
direct/test-framework/stress_tests/stress_tester/yaffs_tagsmarshall.c
direct/test-framework/stress_tests/stress_tester/yaffs_yaffs2.h
direct/test-framework/stress_tests/stress_tester/nandsim_file.c
direct/test-framework/stress_tests/stress_tester/yaffs_allocator.c
direct/test-framework/stress_tests/stress_tester/yportenv.h
direct/test-framework/stress_tests/stress_tester/yaffs_packedtags2.c
direct/test-framework/stress_tests/stress_tester/yaffs_osglue.h
direct/test-framework/stress_tests/stress_tester/yaffs_flashif2.h
direct/test-framework/stress_tests/stress_tester/yaffs_tagsmarshall.h
direct/test-framework/stress_tests/stress_tester/yaffs_endian.h
direct/test-framework/stress_tests/stress_tester/yaffs_nand_drv.h
direct/test-framework/stress_tests/stress_tester/yaffs_nandsim_file.c
direct/test-framework/stress_tests/stress_tester/yaffs_fileem2k.c
direct/test-framework/stress_tests/stress_tester/yaffs_nor_drv.h
direct/test-framework/stress_tests/stress_tester/yaffs_verify.c
direct/test-framework/stress_tests/stress_tester/nandsim_file.h
direct/test-framework/stress_tests/stress_tester/yaffs_packedtags1.c
direct/test-framework/stress_tests/stress_tester/yaffs_nor_drv.c
direct/test-framework/stress_tests/stress_tester/ynorsim.c
direct/test-framework/stress_tests/stress_tester/yaffs_nand.h
direct/test-framework/stress_tests/stress_tester/yaffs_attribs.h
direct/test-framework/stress_tests/stress_tester/yaffs_fileem.c
direct/test-framework/stress_tests/stress_tester/yaffs_yaffs1.c
direct/test-framework/stress_tests/stress_tester/yaffs_guts.h
direct/test-framework/stress_tests/stress_tester/yaffscfg2k.c
direct/test-framework/stress_tests/stress_tester/yaffscfg.h
direct/test-framework/stress_tests/stress_tester/yaffs_flexible_file_sim.h
direct/test-framework/stress_tests/stress_tester/yaffs_guts.c
direct/test-framework/stress_tests/stress_tester/ynorsim.h
direct/test-framework/stress_tests/stress_tester/yaffs_ramdisk.c
direct/test-framework/stress_tests/stress_tester/yaffs_hweight.h
direct/test-framework/stress_tests/stress_tester/yaffs_packedtags1.h
direct/test-framework/stress_tests/stress_tester/yaffs_trace.h
direct/test-framework/stress_tests/stress_tester/yaffs_tagscompat.h
direct/test-framework/stress_tests/stress_tester/yaffs_attribs.c
direct/test-framework/stress_tests/stress_tester/yaffs_ecc.h
direct/test-framework/stress_tests/stress_tester/yaffs_yaffs1.h
direct/test-framework/stress_tests/stress_tester/yaffs_bitmap.c
direct/test-framework/stress_tests/stress_tester/yaffs_nameval.h
direct/test-framework/stress_tests/stress_tester/yaffs_nandemul2k.h
direct/test-framework/stress_tests/stress_tester/yaffs_tagscompat.c
direct/test-framework/stress_tests/stress_tester/yaffs_nandsim_file.h
direct/test-framework/stress_tests/stress_tester/yaffs_osglue.c
direct/test-framework/stress_tests/stress_tester/nandstore_file.c
direct/test-framework/stress_tests/threading/nanddrv.c
direct/test-framework/stress_tests/threading/yaffs_packedtags2.h
direct/test-framework/stress_tests/threading/nandsim.h
direct/test-framework/stress_tests/threading/yaffs_cache.h
direct/test-framework/stress_tests/threading/yaffs_hweight.c
direct/test-framework/stress_tests/threading/yaffs_ramem2k.c
direct/test-framework/stress_tests/threading/yaffs_flexible_file_sim.c
direct/test-framework/stress_tests/threading/yaffs_cache.c
direct/test-framework/stress_tests/threading/yaffs_m18_drv.c
direct/test-framework/stress_tests/threading/nandstore_file.h
direct/test-framework/stress_tests/threading/yaffs_nand.c
direct/test-framework/stress_tests/threading/yaffs_bitmap.h
direct/test-framework/stress_tests/threading/yaffs_yaffs2.c
direct/test-framework/stress_tests/threading/yaffs_fileem2k.h
direct/test-framework/stress_tests/threading/yaffs_checkptrw.h
direct/test-framework/stress_tests/threading/yaffs_error.c
direct/test-framework/stress_tests/threading/yaffs_flashif.h
direct/test-framework/stress_tests/threading/yaffs_getblockinfo.h
direct/test-framework/stress_tests/threading/yaffs_list.h
direct/test-framework/stress_tests/threading/yaffs_ecc.c
direct/test-framework/stress_tests/threading/yaffsfs.h
direct/test-framework/stress_tests/threading/yaffs_nand_drv.c
direct/test-framework/stress_tests/threading/ydirectenv.h
direct/test-framework/stress_tests/threading/yaffs_allocator.h
direct/test-framework/stress_tests/threading/nanddrv.h
direct/test-framework/stress_tests/threading/yaffs_verify.h
direct/test-framework/stress_tests/threading/yaffsfs.c
direct/test-framework/stress_tests/threading/nandsim.c
direct/test-framework/stress_tests/threading/yaffs_ramdisk.h
direct/test-framework/stress_tests/threading/nand_chip.h
direct/test-framework/stress_tests/threading/yaffs_summary.h
direct/test-framework/stress_tests/threading/yaffs_nameval.c
direct/test-framework/stress_tests/threading/yaffs_summary.c
direct/test-framework/stress_tests/threading/yaffs_checkptrw.c
direct/test-framework/stress_tests/threading/nand_store.h
direct/test-framework/stress_tests/threading/yaffs_endian.c
direct/test-framework/stress_tests/threading/yaffs_m18_drv.h
direct/test-framework/stress_tests/threading/yaffs_tagsmarshall.c
direct/test-framework/stress_tests/threading/yaffs_yaffs2.h
direct/test-framework/stress_tests/threading/nandsim_file.c
direct/test-framework/stress_tests/threading/yaffs_allocator.c
direct/test-framework/stress_tests/threading/yportenv.h
direct/test-framework/stress_tests/threading/yaffs_packedtags2.c
direct/test-framework/stress_tests/threading/yaffs_osglue.h
direct/test-framework/stress_tests/threading/yaffs_flashif2.h
direct/test-framework/stress_tests/threading/yaffs_tagsmarshall.h
direct/test-framework/stress_tests/threading/yaffs_endian.h
direct/test-framework/stress_tests/threading/yaffs_nand_drv.h
direct/test-framework/stress_tests/threading/yaffs_nandsim_file.c
direct/test-framework/stress_tests/threading/yaffs_fileem2k.c
direct/test-framework/stress_tests/threading/yaffs_nor_drv.h
direct/test-framework/stress_tests/threading/yaffs_verify.c
direct/test-framework/stress_tests/threading/nandsim_file.h
direct/test-framework/stress_tests/threading/yaffs_packedtags1.c
direct/test-framework/stress_tests/threading/yaffs_nor_drv.c
direct/test-framework/stress_tests/threading/ynorsim.c
direct/test-framework/stress_tests/threading/yaffs_nand.h
direct/test-framework/stress_tests/threading/yaffs_attribs.h
direct/test-framework/stress_tests/threading/yaffs_fileem.c
direct/test-framework/stress_tests/threading/yaffs_yaffs1.c
direct/test-framework/stress_tests/threading/yaffs_guts.h
direct/test-framework/stress_tests/threading/yaffscfg2k.c
direct/test-framework/stress_tests/threading/yaffscfg.h
direct/test-framework/stress_tests/threading/yaffs_flexible_file_sim.h
direct/test-framework/stress_tests/threading/yaffs_guts.c
direct/test-framework/stress_tests/threading/ynorsim.h
direct/test-framework/stress_tests/threading/yaffs_ramdisk.c
direct/test-framework/stress_tests/threading/yaffs_hweight.h
direct/test-framework/stress_tests/threading/yaffs_packedtags1.h
direct/test-framework/stress_tests/threading/yaffs_trace.h
direct/test-framework/stress_tests/threading/yaffs_tagscompat.h
direct/test-framework/stress_tests/threading/yaffs_attribs.c
direct/test-framework/stress_tests/threading/yaffs_ecc.h
direct/test-framework/stress_tests/threading/yaffs_yaffs1.h
direct/test-framework/stress_tests/threading/yaffs_bitmap.c
direct/test-framework/stress_tests/threading/yaffs_nameval.h
direct/test-framework/stress_tests/threading/yaffs_nandemul2k.h
direct/test-framework/stress_tests/threading/yaffs_tagscompat.c
direct/test-framework/stress_tests/threading/yaffs_nandsim_file.h
direct/test-framework/stress_tests/threading/yaffs_osglue.c
direct/test-framework/stress_tests/threading/nandstore_file.c
direct/test-framework/stress_tests/handle_tests/nanddrv.c
direct/test-framework/stress_tests/handle_tests/yaffs_packedtags2.h
direct/test-framework/stress_tests/handle_tests/nandsim.h
direct/test-framework/stress_tests/handle_tests/yaffs_cache.h
direct/test-framework/stress_tests/handle_tests/yaffs_hweight.c
direct/test-framework/stress_tests/handle_tests/yaffs_ramem2k.c
direct/test-framework/stress_tests/handle_tests/yaffs_flexible_file_sim.c
direct/test-framework/stress_tests/handle_tests/yaffs_cache.c
direct/test-framework/stress_tests/handle_tests/yaffs_m18_drv.c
direct/test-framework/stress_tests/handle_tests/nandstore_file.h
direct/test-framework/stress_tests/handle_tests/yaffs_nand.c
direct/test-framework/stress_tests/handle_tests/yaffs_bitmap.h
direct/test-framework/stress_tests/handle_tests/yaffs_yaffs2.c
direct/test-framework/stress_tests/handle_tests/yaffs_fileem2k.h
direct/test-framework/stress_tests/handle_tests/yaffs_checkptrw.h
direct/test-framework/stress_tests/handle_tests/yaffs_error.c
direct/test-framework/stress_tests/handle_tests/yaffs_flashif.h
direct/test-framework/stress_tests/handle_tests/yaffs_getblockinfo.h
direct/test-framework/stress_tests/handle_tests/yaffs_list.h
direct/test-framework/stress_tests/handle_tests/yaffs_ecc.c
direct/test-framework/stress_tests/handle_tests/yaffsfs.h
direct/test-framework/stress_tests/handle_tests/yaffs_nand_drv.c
direct/test-framework/stress_tests/handle_tests/ydirectenv.h
direct/test-framework/stress_tests/handle_tests/yaffs_allocator.h
direct/test-framework/stress_tests/handle_tests/nanddrv.h
direct/test-framework/stress_tests/handle_tests/yaffs_verify.h
direct/test-framework/stress_tests/handle_tests/yaffsfs.c
direct/test-framework/stress_tests/handle_tests/nandsim.c
direct/test-framework/stress_tests/handle_tests/yaffs_ramdisk.h
direct/test-framework/stress_tests/handle_tests/nand_chip.h
direct/test-framework/stress_tests/handle_tests/yaffs_summary.h
direct/test-framework/stress_tests/handle_tests/yaffs_nameval.c
direct/test-framework/stress_tests/handle_tests/yaffs_summary.c
direct/test-framework/stress_tests/handle_tests/yaffs_checkptrw.c
direct/test-framework/stress_tests/handle_tests/nand_store.h
direct/test-framework/stress_tests/handle_tests/yaffs_endian.c
direct/test-framework/stress_tests/handle_tests/yaffs_m18_drv.h
direct/test-framework/stress_tests/handle_tests/yaffs_tagsmarshall.c
direct/test-framework/stress_tests/handle_tests/yaffs_yaffs2.h
direct/test-framework/stress_tests/handle_tests/nandsim_file.c
direct/test-framework/stress_tests/handle_tests/yaffs_allocator.c
direct/test-framework/stress_tests/handle_tests/yportenv.h
direct/test-framework/stress_tests/handle_tests/yaffs_packedtags2.c
direct/test-framework/stress_tests/handle_tests/yaffs_osglue.h
direct/test-framework/stress_tests/handle_tests/yaffs_flashif2.h
direct/test-framework/stress_tests/handle_tests/yaffs_tagsmarshall.h
direct/test-framework/stress_tests/handle_tests/yaffs_endian.h
direct/test-framework/stress_tests/handle_tests/yaffs_nand_drv.h
direct/test-framework/stress_tests/handle_tests/yaffs_nandsim_file.c
direct/test-framework/stress_tests/handle_tests/yaffs_fileem2k.c
direct/test-framework/stress_tests/handle_tests/yaffs_nor_drv.h
direct/test-framework/stress_tests/handle_tests/yaffs_verify.c
direct/test-framework/stress_tests/handle_tests/nandsim_file.h
direct/test-framework/stress_tests/handle_tests/yaffs_packedtags1.c
direct/test-framework/stress_tests/handle_tests/yaffs_nor_drv.c
direct/test-framework/stress_tests/handle_tests/ynorsim.c
direct/test-framework/stress_tests/handle_tests/yaffs_nand.h
direct/test-framework/stress_tests/handle_tests/yaffs_attribs.h
direct/test-framework/stress_tests/handle_tests/yaffs_fileem.c
direct/test-framework/stress_tests/handle_tests/yaffs_yaffs1.c
direct/test-framework/stress_tests/handle_tests/yaffs_guts.h
direct/test-framework/stress_tests/handle_tests/yaffscfg2k.c
direct/test-framework/stress_tests/handle_tests/yaffscfg.h
direct/test-framework/stress_tests/handle_tests/yaffs_flexible_file_sim.h
direct/test-framework/stress_tests/handle_tests/yaffs_guts.c
direct/test-framework/stress_tests/handle_tests/ynorsim.h
direct/test-framework/stress_tests/handle_tests/yaffs_ramdisk.c
direct/test-framework/stress_tests/handle_tests/yaffs_hweight.h
direct/test-framework/stress_tests/handle_tests/yaffs_packedtags1.h
direct/test-framework/stress_tests/handle_tests/yaffs_trace.h
direct/test-framework/stress_tests/handle_tests/yaffs_tagscompat.h
direct/test-framework/stress_tests/handle_tests/yaffs_attribs.c
direct/test-framework/stress_tests/handle_tests/yaffs_ecc.h
direct/test-framework/stress_tests/handle_tests/yaffs_yaffs1.h
direct/test-framework/stress_tests/handle_tests/yaffs_bitmap.c
direct/test-framework/stress_tests/handle_tests/yaffs_nameval.h
direct/test-framework/stress_tests/handle_tests/yaffs_nandemul2k.h
direct/test-framework/stress_tests/handle_tests/yaffs_tagscompat.c
direct/test-framework/stress_tests/handle_tests/yaffs_nandsim_file.h
direct/test-framework/stress_tests/handle_tests/yaffs_osglue.c
direct/test-framework/stress_tests/handle_tests/nandstore_file.c
