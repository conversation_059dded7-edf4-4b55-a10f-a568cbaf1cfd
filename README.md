# Yaffs: Yet Another Flash File System

### _A Robust Flash File System Since 2002_

[Yaffs](https://yaffs.net) (Yet Another Flash File System) is an open-source file system specifically
designed to be fast, robust and suitable for embedded use with NAND and NOR Flash.
It is widely used with Linux, RTOSs, or no OS at all, in consumer devices, 
avionics, and critical infrastructure. 

## License

Yaffs covered by copyright and is the property of Aleph One Limited. 

* Yaffs is available free of charge under the [GNU General Public Licence, version
  2](https://www.gnu.org/licenses/old-licenses/gpl-2.0.html) (GPL).

* Linking Yaffs to other software creates a derived work and our interpretation of
  linking includes dynamic and runtime linking; any use of Yaffs creates a derived work.

* Under the GPL, you can test Yaffs with your product on your computers. Do not 
  distribute this test version unless your product is normally licensed under the
  GPL. If you do not copy and distribute it, there are no requirement under the GPL
  regarding distribution of a derived work.  

* Under the terms of the GPL, derived works must be licensed using the GPL or a
  "compatible license", unless you have a Commercial Licence for Yaffs. For the
  avoidance of doubt, we deem version 3 of the GPL to be compatible and derived
  works licensed under GPLv3 are fine without a commercial license.

* When you distribute GPLed object code you must make the full source code available
  too, to anyone who uses your product. (Sections 3.a and 3.b of the GPL, and the
  sections that follow.)  

Circumstances where you need to licence Yaffs other than under the GPL include:

* If you want to use Yaffs, but want to distribute your code under your licence.

* If you want to run a statically-linked commercial RTOS on top of a Yaffs filesystem.

* If you want to sublicence Yaffs to your customers (under terms other than the GPL).

* If you want to use Yaffs with WindowsCE version 4. WinCE version 3 can use GPL
  code but WinCE v.4 and later need an additional filesystem interface layer which
  is not part of Yaffs. This is available at extra cost; please contact us.

In these cases, our Commercial Licence is appropriate.

If you aren't sure whether your needs will be covered by the GPL version, get in 
touch with us.

## Support
While Yaffs is released under GPL we can provide standard support options, 
consulting in its application to your particular product, or development of specific 
features to suit your needs or hardware.

## Helping us to improve Yaffs
Although the code is downloadable at no charge, buying this product allows you to 
express your appreciation of Yaffs. Alternatively you can make a Donation to Aleph 
One Ltd via PayPal. You can do this at PayPal by sending any sum to 
<EMAIL>  We shall appreciate it!

