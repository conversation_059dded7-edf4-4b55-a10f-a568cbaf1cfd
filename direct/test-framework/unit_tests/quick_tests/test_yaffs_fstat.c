/*
 * YAFFS: Yet another FFS. A NAND-flash specific file system.
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#include "test_yaffs_fstat.h"

static int handle = -1;
int test_yaffs_fstat(void)
{
	struct yaffs_stat stat;
	handle = test_yaffs_open();

	if (handle >= 0){
		return 	yaffs_fstat(handle , &stat);
	} else {
		print_message( "error opening file\n", 2 );
		return -1;
	}
}

int test_yaffs_fstat_clean(void)
{
	if (handle <= 0){
		return yaffs_close(handle);
	}
	return 1;
}
