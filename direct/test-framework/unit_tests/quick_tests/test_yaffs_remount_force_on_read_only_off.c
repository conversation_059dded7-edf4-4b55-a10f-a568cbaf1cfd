/*
 * YAFFS: Yet another FFS. A NAND-flash specific file system.
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#include "test_yaffs_remount_force_on_read_only_off.h"
#include "test_yaffs_open.h"

int test_yaffs_remount_force_on_read_only_off(void)
{
	int output = -1;

	output=test_yaffs_open();
	if (output<0) {
		print_message("failed to open file\n",2);
		return -1;
	}

	output = yaffs_remount(YAFFS_MOUNT_POINT,1,0);
	if (output>=0){
		return 1;
	} else {
		print_message("failed to unmount with a open file with the force mode set\n",2);
		return -1;
	}
}

int test_yaffs_remount_force_on_read_only_off_clean(void)
{
	int output=0;
	int error_code =0;
	output= yaffs_mount(YAFFS_MOUNT_POINT);
	if (output<0){
		error_code=yaffs_get_error();
		if (abs(error_code) == EBUSY){
			return 1;
		} else {
			return -1;
		}
	}
	return 1;
}
