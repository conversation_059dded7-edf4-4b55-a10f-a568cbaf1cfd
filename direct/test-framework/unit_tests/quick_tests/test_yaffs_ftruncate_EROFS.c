/*
 * YAFFS: Yet another FFS. A NAND-flash specific file system.
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#include "test_yaffs_ftruncate_EROFS.h"

static int handle = -1;

int test_yaffs_ftruncate_EROFS(void)
{
	int output = 0;
	int error_code = 0;
	handle = yaffs_open(FILE_PATH,O_CREAT | O_RDWR  , S_IREAD | S_IWRITE );
	if (handle<0){
		print_message("failed to open file\n",2);
		return -1;
	}
	EROFS_setup();
	output = yaffs_ftruncate(handle,FILE_SIZE_TRUNCATED );
	if (output < 0){
		error_code=yaffs_get_error();
		//printf("EISDIR def %d, Error code %d\n", EISDIR,error_code);
		if (abs(error_code) == EROFS){
			return 1;
		} else {
			print_message("different error than expected\n", 2);
			return -1;
		}
	} else {
		print_message("file truncated with EROFS set.(which is a bad thing)\n", 2);
		return -1;
	}
}

int test_yaffs_ftruncate_EROFS_clean(void)
{
	int output=1;
	if (handle >= 0) {
		output= yaffs_close(handle);
	}
	return (EROFS_clean() && output);
}
