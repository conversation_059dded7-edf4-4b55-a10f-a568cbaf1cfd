Made by <PERSON> <<EMAIL>> on 04/11/2010

This test is designed to test the response and error handling of the yaffs functions.

compile command: make
run command: ./quick_tests

command line options:
	-h 				prints the available command line options.
	-c				tests will continue even if a test fails, rather than exiting the test.
	-v 				verbose mode. will print all messages.
	-q				quiet mode. will not print any messages.
	-t [number]		sets the yaffs_trace() function to the number.
	-n [number]		sets the number of randomly selected tests to run after the test has run through 
					after running all of listed tests.

############################# development infomation ####################################################################

Tests made

test_yaffs_access.c
test_yaffs_access_EACCES.c
test_yaffs_access_EINVAL.c
test_yaffs_access_ELOOP.c
test_yaffs_access_ELOOP_dir.c
test_yaffs_access_ENAMETOOLONG.c
test_yaffs_access_ENOENT2.c
test_yaffs_access_ENOENT.c
test_yaffs_access_ENOTDIR.c
test_yaffs_access_EROFS.c
test_yaffs_access_NULL.c

test_yaffs_chmod.c
test_yaffs_chmod_EINVAL.c
test_yaffs_chmod_ELOOP.c
test_yaffs_chmod_ELOOP_dir.c
test_yaffs_chmod_ENAMETOOLONG.c
test_yaffs_chmod_ENOENT2.c
test_yaffs_chmod_ENOENT.c
test_yaffs_chmod_ENOTDIR.c
test_yaffs_chmod_EROFS.c
test_yaffs_chmod_NULL.c

test_yaffs_close.c
test_yaffs_close_EBADF.c
test_yaffs_close_NULL.c

test_yaffs_dup.c
test_yaffs_dup_EBADF.c	

test_yaffs_fchmod.c
test_yaffs_fchmod_EBADF.c
test_yaffs_fchmod_EINVAL.c
test_yaffs_fchmod_EROFS.c
test_yaffs_fchmod_NULL.c

test_yaffs_fdatasync.c
test_yaffs_fdatasync_EBADF.c
test_yaffs_fdatasync_EROFS.c
test_yaffs_fdatasync_NULL.c

test_yaffs_flush.c
test_yaffs_flush_EBADF.c
test_yaffs_flush_EROFS.c

test_yaffs_freespace.c
test_yaffs_freespace_EINVAL.c
test_yaffs_freespace_ENAMETOOLONG.c
test_yaffs_freespace_NULL.c

test_yaffs_fstat.c
test_yaffs_fstat_EBADF.c

test_yaffs_fsync.c
test_yaffs_fsync_EBADF.c
test_yaffs_fsync_EROFS.c

test_yaffs_ftruncate_big_file.c
test_yaffs_ftruncate.c
test_yaffs_ftruncate_EBADF.c
test_yaffs_ftruncate_EINVAL.c
test_yaffs_ftruncate_EROFS.c

test_yaffs_inodecount.c
test_yaffs_inodecount_EINVAL.c
test_yaffs_inodecount_ENAMETOOLONG.c
test_yaffs_inodecount_NULL.c

test_yaffs_link.c
test_yaffs_link_EEXIST.c
test_yaffs_link_ELOOP_dir.c
test_yaffs_link_ENAMETOOLONG2.c
test_yaffs_link_ENAMETOOLONG.c
test_yaffs_link_ENOENT2.c
test_yaffs_link_ENOENT3.c
test_yaffs_link_ENOENT4.c
test_yaffs_link_ENOENT.c
test_yaffs_link_ENOTDIR2.c
test_yaffs_link_ENOTDIR.c
test_yaffs_link_EROFS.c
test_yaffs_link_NULL2.c
test_yaffs_link_NULL.c

test_yaffs_lseek_big_file.c
test_yaffs_lseek.c
test_yaffs_lseek_EBADF.c
test_yaffs_lseek_EINVAL.c
test_yaffs_lseek_EROFS.c

test_yaffs_lstat.c
test_yaffs_lstat_ENAMETOOLONG.c
test_yaffs_lstat_ENOENT.c
test_yaffs_lstat_ENOTDIR.c
test_yaffs_lstat_NULL.c

test_yaffs_mkdir.c
test_yaffs_mkdir_EEXIST.c
test_yaffs_mkdir_ELOOP_dir.c
test_yaffs_mkdir_ENAMETOOLONG.c
test_yaffs_mkdir_ENOENT.c
test_yaffs_mkdir_ENOTDIR.c
test_yaffs_mkdir_EROFS.c
test_yaffs_mkdir_NULL.c

test_yaffs_mount2.c
test_yaffs_mount2_ENODEV.c

test_yaffs_mount.c
test_yaffs_mount_EBUSY.c
test_yaffs_mount_ENAMETOOLONG.c
test_yaffs_mount_ENODEV.c
test_yaffs_mount_NULL.c

test_yaffs_open.c
test_yaffs_open_EACCES.c
test_yaffs_open_EEXIST.c
test_yaffs_open_EINVAL2.c
test_yaffs_open_EINVAL.c
test_yaffs_open_EISDIR.c
test_yaffs_open_ELOOP.c
test_yaffs_open_ELOOP_dir.c
test_yaffs_open_ENAMETOOLONG.c
test_yaffs_open_ENOENT.c
test_yaffs_open_ENOTDIR.c
test_yaffs_open_EROFS.c
test_yaffs_open_NULL.c

test_yaffs_read.c
test_yaffs_read_EBADF.c
test_yaffs_read_EINVAL.c

test_yaffs_remount_EINVAL.c
test_yaffs_remount_ENAMETOOLONG.c
test_yaffs_remount_ENODEV.c
test_yaffs_remount_force_off_read_only_off.c
test_yaffs_remount_force_off_read_only_on.c
test_yaffs_remount_force_on_read_only_off.c
test_yaffs_remount_force_on_read_only_on.c
test_yaffs_remount_NULL.c

test_yaffs_rename.c
test_yaffs_rename_dir.c
test_yaffs_rename_dir_ENOENT2.c
test_yaffs_rename_dir_ENOENT.c
test_yaffs_rename_dir_not_empty.c
test_yaffs_rename_dir_to_file.c
test_yaffs_rename_EEXISTS.c
test_yaffs_rename_EINVAL.c
test_yaffs_rename_ELOOP_dir.c
test_yaffs_rename_ENAMETOOLONG2.c
test_yaffs_rename_ENAMETOOLONG.c
test_yaffs_rename_ENOENT.c
test_yaffs_rename_ENOTDIR.c
test_yaffs_rename_EROFS.c
test_yaffs_rename_file_to_dir.c
test_yaffs_rename_NULL2.c
test_yaffs_rename_NULL.c

test_yaffs_rmdir.c
test_yaffs_rmdir_EBUSY.c
test_yaffs_rmdir_EINVAL.c
test_yaffs_rmdir_ELOOP_dir.c
test_yaffs_rmdir_ENAMETOOLONG.c
test_yaffs_rmdir_ENOENT.c
test_yaffs_rmdir_ENOTDIR.c
test_yaffs_rmdir_EROFS.c
test_yaffs_rmdir_NULL.c

test_yaffs_stat.c
test_yaffs_stat_ELOOP.c
test_yaffs_stat_ELOOP_dir.c
test_yaffs_stat_ENAMETOOLONG.c
test_yaffs_stat_ENOENT2.c
test_yaffs_stat_ENOENT.c
test_yaffs_stat_ENOTDIR.c
test_yaffs_stat_NULL.c

test_yaffs_symlink.c
test_yaffs_symlink_EEXIST.c
test_yaffs_symlink_ELOOP_dir.c
test_yaffs_symlink_ENAMETOOLONG.c
test_yaffs_symlink_ENOENT2.c
test_yaffs_symlink_ENOENT.c
test_yaffs_symlink_ENOTDIR.c
test_yaffs_symlink_EROFS.c
test_yaffs_symlink_NULL2.c
test_yaffs_symlink_NULL.c

test_yaffs_sync.c
test_yaffs_sync_ENAMETOOLONG.c
test_yaffs_sync_ENODEV.c
test_yaffs_sync_EROFS.c
test_yaffs_sync_NULL.c

test_yaffs_totalspace.c
test_yaffs_totalspace_EINVAL.c
test_yaffs_totalspace_ENAMETOOLONG.c
test_yaffs_totalspace_NULL.c

test_yaffs_truncate_big_file.c
test_yaffs_truncate.c
test_yaffs_truncate_EINVAL.c
test_yaffs_truncate_EISDIR.c
test_yaffs_truncate_ELOOP.c
test_yaffs_truncate_ELOOP_dir.c
test_yaffs_truncate_ENAMETOOLONG.c
test_yaffs_truncate_ENOENT2.c
test_yaffs_truncate_ENOENT.c
test_yaffs_truncate_ENOTDIR.c
test_yaffs_truncate_EROFS.c
test_yaffs_truncate_NULL.c

test_yaffs_unlink.c
test_yaffs_unlink_EISDIR.c
test_yaffs_unlink_ELOOP_dir.c
test_yaffs_unlink_ENAMETOOLONG.c
test_yaffs_unlink_ENOENT2.c
test_yaffs_unlink_ENOENT.c
test_yaffs_unlink_ENOTDIR.c
test_yaffs_unlink_EROFS.c
test_yaffs_unlink_NULL.c

test_yaffs_unmount2.c
test_yaffs_unmount2_EINVAL.c
test_yaffs_unmount2_ENODEV.c
test_yaffs_unmount2_with_handle_open_and_forced_mode_off.c
test_yaffs_unmount2_with_handle_open_and_forced_mode_on.c
test_yaffs_unmount.c
test_yaffs_unmount_EBUSY.c
test_yaffs_unmount_ENAMETOOLONG.c
test_yaffs_unmount_ENODEV.c
test_yaffs_unmount_NULL.c

test_yaffs_write_big_file.c
test_yaffs_write.c
test_yaffs_write_EBADF.c
test_yaffs_write_EROFS.c



Tests to add

	test_yaffs_rename a file over its self.

	test_yaffs_readlink
	test_yaffs_readlink_ENOENT
	test_yaffs_readlink_ENOTDIR
	test_yaffs_readlink_ELOOP

	test_yaffs_mount2_with read only set

	test_yaffs_remount_force_off_read_only_on
	test_yaffs_remount_force_on_read_only_on

	test_yaffs_opendir

	test_yaffs_readdir

	test_yaffs_rewinddir

	test_yaffs_closedir

	test_yaffs_link_EPERM

	test_yaffs_rmdir_ENOTEMPTY

	test_yaffs_rename_EMLINK
	test_yaffs_rename_EEXISTS or EPERM	

	test_yaffs_open_ENOSPC

	test yaffs_open_running_out_of_handles error

	test_yaffs_read_big_file
	test what happens if you read off the end of the file?

	test_yaffs_pread
	test_yaffs_pread_EBADF
	test_yaffs_pread_EINVAL
	test_yaffs_pread_big_file

	test_yaffs_write_big_file
	test_yaffs_write_EINVAL
	What happens when you run out of space?
	

	test_yaffs_pwrite_big_file
	test_yaffs_pwrite_EINVAL

	test_yaffs_unlink_ENOMEM

	test_yaffs_access_ENOENT_generated_with_a_dangling_symbloic_link

	Add a truncate function for truncating a file size to -1.

	What happens if a handle is opened to a file and the file is then deleted?
	Check to see if yaffs generates an error code for no reason.
	What happens when a file is opened with no modes set?

	What happens when yaffs is unmounted twice?

	What happens when open a handle, unmount yaffs and then try to use the handle? 

	What happens when a mount point is mounted using mount2 with read only mode set and then a file is chmoded? 

	Try to remove lost and found dir and replace it with a file of the same name.

	Change the mode of the lost and found dir to read only.

Tests which do not exist in yaffs:
	test_yaffs_close	//This function has already been called by the time this test is reached, 
				//so there is no point in testing it.

	test_yaffs_link_EMLINK		//should not happen on yaffs
	test_yaffs_link_ELOOP
	
	test_yaffs_lstat_ELOOP

	test_yaffs_mkdir_ELOOP

	//the yaffs_mknod function does not exist in yaffsfs, so these tests will not be added.
	test_yaffs_mknod_EACCES
	test_yaffs_mknod_EEXIST
	test_yaffs_mknod_EINVAL
	test_yaffs_mknod_ELOOP
	test_yaffs_mknod_ENAMETOOLONG
	test_yaffs_mknod_ENOENT
	test_yaffs_mknod_ENOTDIR
	test_yaffs_mknod_EROFS

	teat_yaffs_mount_ELOOP

	test_yaffs_mount2_EINVAL	//cannot happen in yaffs since the mode is not checked.
	test_yaffs_mount2_ENOTDIR	//cannot be generated in yaffs.
	test_yaffs_mount2_ENOENT	//cannot be generated in yaffs.
	test_yaffs_mount2_ELOOP

	test_yaffs_remount_ENOENT	//cannot be generated in yaffs
	test_yaffs_remount_ENOTDIR	//cannot be generated in yaffs

	test_yaffs_unmount2_ENOENT	//cannot be generated in yaffs.
	test_yaffs_unmount2_ENOTDIR	//cannot be generated in yaffs.

How to add a test
	First create the test .c and .h file.
	The file name and test function name should be the same. 
	This name should follow one of these formats: 
	Test_yaffs_[function of yaffs which is been tested]
	Test_yaffs_[function of yaffs which is been tested]_[error trying to be generated]
	
	The .c file needs to contain two functions.
	The first function needs to contain the code for the main test and will 
	return -1 on a failure and 0 or greater on a success.
	The second function needs contain the code for cleaning up after the test. 
	Cleaning up may include closing some open handles, recreating a file, ect. 
	This second function needs to return -1 on a failure and 0 or greater on success.

	The name of first function needs to be called the same as the file 
	name (without the .c or .h)
	The second function's name needs be the same as the first function but 
	with "_clean" added on the end.
	
	So if a test is been created for the yaffs function yaffs_foo() then 
	create these files
	test_yaffs_foo.c
		Contains int test_yaffs_foo(void); int test_yaffs_foo_clean(void);
	test_yaffs_foo.h
		Which includes "lib.h", "yaffsfs.h" header files.

	Next write the test code in these files then add these files to the Makefile.

	Add the name of the test files' object file (test_yaffs_foo.o ) to the 
	TESTFILES tag around line 50 of the Makefile.	

	Now add the test functions to the test_list[] array in quick_tests.h
	The order of the tests matters. The idea is to test each yaffs_function 
	individualy and only using tested yaffs_components before using this new 
	yaffs_function. 
	This array consists of: 
	{[test function], [the clean function], [name of the tests which will be printed when the test fails]},	
	
	So add this line to the test_list[]: {test_yaffs_foo, test_yaffs_foo_clean, "test_yaffs_foo"},

	Also include the test's .h file in the quick_test.h file: #include "test_yaffs_foo.h"
	
	The test file should now make and run(you may need to make clean first). 



	PS: yaffs_foo() is a made up function for this README (in case you want 
	to find this function in yaffs). 






