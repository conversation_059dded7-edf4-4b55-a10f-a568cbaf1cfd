/*
 * YAFFS: Yet another FFS. A NAND-flash specific file system.
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#include "test_b.h"

void test_b(void *x)
{
	struct bovver_context *bc = (struct bovver_context *)x;
	int n = rand() % 20;
	
	bc->cycle++;

	if(!bc->dirH)
		bc->dirH = yaffs_opendir(bc->baseDir);

	if(!bc->dirH)
		return;

	if(n == 0){
		yaffs_closedir(bc->dirH);
		bc->dirH = NULL;
	} else {
		while(n > 1){
			n--;
			yaffs_readdir(bc->dirH);
		}
	}
}

