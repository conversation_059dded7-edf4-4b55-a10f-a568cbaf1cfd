/*
 * YAFFS: Yet another FFS. A NAND-flash specific file system.
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#include "yaffsfs.h"

struct error_entry {
	int code;
	const char *text;
};

static const struct error_entry error_list[] = {
	{ ENOMEM , "ENOMEM" },
	{ EBUSY , "EBUSY"},
	{ ENODEV , "ENODEV"},
	{ EINVAL , "EINVAL"},
	{ EBADF , "EBADF"},
	{ EACCES , "EACCES"},
	{ EXDEV , "EXDEV" },
	{ ENOENT , "ENOENT"},
	{ ENOSPC , "ENOSPC"},
	{ ERANGE , "ERANGE"},
	{ ENODATA, "ENODATA"},
	{ ENOTEMPTY, "ENOTEMPTY"},
	{ <PERSON>NAMETOOLONG, "<PERSON>NA<PERSON><PERSON><PERSON>ON<PERSON>"},
	{ <PERSON>NOMEM , "ENOMEM"},
	{ EEXIST , "EEXIST"},
	{ ENOTDIR , "ENOTDIR"},
	{ EISDIR , "EISDIR"},
	{ ENFILE, "ENFILE"},
	{ EROFS, "EROFS"},
	{ EFAULT, "EFAULT"},
	{ 0, NULL }
};

const char *yaffs_error_to_str(int err)
{
	const struct error_entry *e = error_list;

	if (err < 0)
		err = -err;

	while (e->code && e->text) {
		if (err == e->code)
			return e->text;
		e++;
	}
	return "Unknown error code";
}
